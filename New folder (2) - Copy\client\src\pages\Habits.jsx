import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaFire, FaTrophy, FaCheck, FaUndo, FaEdit, FaTrash } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';
import toast from 'react-hot-toast';

const Habits = () => {
  const [habits, setHabits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [todayProgress, setTodayProgress] = useState({ completed: 0, total: 0, percentage: 0 });
  const { user } = useAuth();
  const { t } = useLanguage();

  const categories = [
    { value: 'all', label: 'All Habits', icon: '🌍' },
    { value: 'transportation', label: 'Transportation', icon: '🚌' },
    { value: 'energy', label: 'Energy', icon: '💡' },
    { value: 'water', label: 'Water', icon: '💧' },
    { value: 'waste', label: 'Waste', icon: '♻️' },
    { value: 'food', label: 'Food', icon: '🥗' },
    { value: 'lifestyle', label: 'Lifestyle', icon: '🌱' }
  ];

  useEffect(() => {
    fetchHabits();
  }, []);

  const fetchHabits = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/habits/user');
      setHabits(response.data.habits);
      setTodayProgress(response.data.todayProgress);
    } catch (error) {
      console.error('Error fetching habits:', error);
      toast.error('Failed to load habits');
    } finally {
      setLoading(false);
    }
  };

  const completeHabit = async (habitId) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const response = await axios.post('/tracking/complete', {
        habitId,
        date: today,
        mood: 'happy'
      });

      // Update habits list
      setHabits(habits.map(habit =>
        habit._id === habitId
          ? { ...habit, completedToday: true, streak: response.data.newStreak }
          : habit
      ));

      // Update progress
      setTodayProgress(prev => ({
        ...prev,
        completed: prev.completed + 1,
        percentage: Math.round(((prev.completed + 1) / prev.total) * 100)
      }));

      toast.success(`🎉 ${response.data.message} +${response.data.pointsEarned} points!`);

      // Show new badges if any
      if (response.data.newBadges && response.data.newBadges.length > 0) {
        response.data.newBadges.forEach(badge => {
          toast.success(`🏆 New Badge: ${badge.name}!`, { duration: 5000 });
        });
      }
    } catch (error) {
      console.error('Error completing habit:', error);
      toast.error(error.response?.data?.message || 'Failed to complete habit');
    }
  };

  const undoHabit = async (habitId) => {
    try {
      // Find the tracking record for today
      const today = new Date().toISOString().split('T')[0];
      const response = await axios.get(`/tracking/date/${today}`);
      const tracking = response.data.tracking.find(t => t.habit._id === habitId);

      if (tracking) {
        await axios.delete(`/tracking/${tracking._id}`);

        // Update habits list
        setHabits(habits.map(habit =>
          habit._id === habitId
            ? { ...habit, completedToday: false }
            : habit
        ));

        // Update progress
        setTodayProgress(prev => ({
          ...prev,
          completed: Math.max(0, prev.completed - 1),
          percentage: Math.round((Math.max(0, prev.completed - 1) / prev.total) * 100)
        }));

        toast.success('Habit completion undone');
      }
    } catch (error) {
      console.error('Error undoing habit:', error);
      toast.error('Failed to undo habit');
    }
  };

  const filteredHabits = selectedCategory === 'all'
    ? habits
    : habits.filter(habit => habit.category === selectedCategory);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Daily Habits 🌱</h1>
            <p className="text-gray-600">Track your eco-friendly habits and build a sustainable lifestyle</p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-eco flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Custom Habit</span>
          </button>
        </div>

        {/* Progress Overview */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="card text-center">
            <div className="text-3xl mb-2">📊</div>
            <div className="text-2xl font-bold text-gray-900">{todayProgress.percentage}%</div>
            <div className="text-gray-600">Today's Progress</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${todayProgress.percentage}%` }}
              ></div>
            </div>
          </div>

          <div className="card text-center">
            <div className="text-3xl mb-2">✅</div>
            <div className="text-2xl font-bold text-gray-900">
              {todayProgress.completed}/{todayProgress.total}
            </div>
            <div className="text-gray-600">Habits Completed</div>
          </div>

          <div className="card text-center">
            <div className="text-3xl mb-2">🔥</div>
            <div className="text-2xl font-bold text-gray-900">{user?.ecoProfile?.streak || 0}</div>
            <div className="text-gray-600">Current Streak</div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2 mb-6">
          {categories.map(category => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.value
                  ? 'bg-green-500 text-white'
                  : 'bg-white text-gray-700 hover:bg-green-50'
              }`}
            >
              <span className="mr-2">{category.icon}</span>
              {category.label}
            </button>
          ))}
        </div>

        {/* Habits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {filteredHabits.map((habit) => (
              <motion.div
                key={habit._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`habit-card ${habit.completedToday ? 'completed' : ''}`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{habit.icon}</div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{habit.name}</h3>
                      <p className="text-sm text-gray-600">{habit.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="badge badge-info">{habit.points} pts</span>
                    {habit.streak > 0 && (
                      <span className="badge badge-warning flex items-center space-x-1">
                        <FaFire className="text-orange-500" />
                        <span>{habit.streak}</span>
                      </span>
                    )}
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4">{habit.description}</p>

                <div className="flex justify-between items-center">
                  {habit.completedToday ? (
                    <button
                      onClick={() => undoHabit(habit._id)}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      <FaUndo />
                      <span>Undo</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => completeHabit(habit._id)}
                      className="btn-eco flex items-center space-x-2"
                    >
                      <FaCheck />
                      <span>Complete</span>
                    </button>
                  )}

                  <div className="text-sm text-gray-500">
                    {habit.totalCompletions} times completed
                  </div>
                </div>

                {habit.completedToday && (
                  <div className="mt-3 p-2 bg-green-50 rounded-lg">
                    <div className="flex items-center space-x-2 text-green-700">
                      <FaTrophy />
                      <span className="text-sm font-medium">Completed today! 🎉</span>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {filteredHabits.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🌱</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No habits found</h3>
            <p className="text-gray-600 mb-4">
              {selectedCategory === 'all'
                ? "Start your eco-friendly journey by adding some habits!"
                : `No habits found in the ${selectedCategory} category.`
              }
            </p>
            <button
              onClick={() => setShowAddForm(true)}
              className="btn-eco"
            >
              Add Your First Habit
            </button>
          </div>
        )}
      </div>

      {/* Add Habit Modal */}
      <AddHabitModal
        isOpen={showAddForm}
        onClose={() => setShowAddForm(false)}
        onHabitAdded={fetchHabits}
      />
    </div>
  );
};

// Add Habit Modal Component
const AddHabitModal = ({ isOpen, onClose, onHabitAdded }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'lifestyle',
    points: 10,
    difficulty: 'easy',
    icon: '🌱'
  });
  const [loading, setLoading] = useState(false);

  const icons = ['🌱', '♻️', '💡', '💧', '🚌', '🥗', '🚶‍♂️', '🔌', '🌿', '🌍'];

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      await axios.post('/habits', formData);
      toast.success('Custom habit created successfully!');
      onHabitAdded();
      onClose();
      setFormData({
        name: '',
        description: '',
        category: 'lifestyle',
        points: 10,
        difficulty: 'easy',
        icon: '🌱'
      });
    } catch (error) {
      console.error('Error creating habit:', error);
      toast.error(error.response?.data?.message || 'Failed to create habit');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl p-6 w-full max-w-md"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">Add Custom Habit</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Habit Name
            </label>
            <input
              type="text"
              required
              className="input-field"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              placeholder="e.g., Use reusable water bottle"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              className="input-field"
              rows="3"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Describe your habit..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                className="input-field"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
              >
                <option value="transportation">Transportation</option>
                <option value="energy">Energy</option>
                <option value="water">Water</option>
                <option value="waste">Waste</option>
                <option value="food">Food</option>
                <option value="lifestyle">Lifestyle</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Points
              </label>
              <input
                type="number"
                min="1"
                max="100"
                className="input-field"
                value={formData.points}
                onChange={(e) => setFormData({...formData, points: parseInt(e.target.value)})}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Icon
            </label>
            <div className="flex flex-wrap gap-2">
              {icons.map(icon => (
                <button
                  key={icon}
                  type="button"
                  onClick={() => setFormData({...formData, icon})}
                  className={`p-2 rounded-lg text-xl ${
                    formData.icon === icon ? 'bg-green-100 border-2 border-green-500' : 'bg-gray-100'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-eco flex-1"
            >
              {loading ? 'Creating...' : 'Create Habit'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default Habits;
