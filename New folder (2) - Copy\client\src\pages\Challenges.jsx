import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTrophy, FaPlus, FaUsers, FaClock, FaFire, FaCheck, FaCalendarAlt, Fa<PERSON>ilter, <PERSON>aEye } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';
import toast from 'react-hot-toast';

const Challenges = () => {
  const [challenges, setChallenges] = useState([]);
  const [userChallenges, setUserChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('active');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const { user } = useAuth();
  const { t } = useLanguage();

  const categories = [
    { value: 'all', label: 'All Categories', icon: '🌍' },
    { value: 'transportation', label: 'Transportation', icon: '🚌' },
    { value: 'energy', label: 'Energy', icon: '💡' },
    { value: 'water', label: 'Water', icon: '💧' },
    { value: 'waste', label: 'Waste', icon: '♻️' },
    { value: 'food', label: 'Food', icon: '🥗' },
    { value: 'lifestyle', label: 'Lifestyle', icon: '🌱' },
    { value: 'community', label: 'Community', icon: '👥' }
  ];

  const tabs = [
    { id: 'active', label: 'Active Challenges', icon: <FaFire /> },
    { id: 'my-challenges', label: 'My Challenges', icon: <FaUsers /> },
    { id: 'upcoming', label: 'Upcoming', icon: <FaClock /> },
    { id: 'completed', label: 'Completed', icon: <FaCheck /> }
  ];

  useEffect(() => {
    fetchChallenges();
    if (activeTab === 'my-challenges') {
      fetchUserChallenges();
    }
  }, [activeTab, categoryFilter]);

  const fetchChallenges = async () => {
    try {
      setLoading(true);
      const status = activeTab === 'upcoming' ? 'upcoming' :
                   activeTab === 'completed' ? 'completed' : 'active';

      const params = new URLSearchParams({
        status,
        ...(categoryFilter !== 'all' && { category: categoryFilter })
      });

      const response = await axios.get(`/challenges?${params}`);
      setChallenges(response.data.challenges);
    } catch (error) {
      console.error('Error fetching challenges:', error);
      toast.error('Failed to load challenges');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserChallenges = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/challenges/user/participating');
      setUserChallenges(response.data.challenges);
    } catch (error) {
      console.error('Error fetching user challenges:', error);
      toast.error('Failed to load your challenges');
    } finally {
      setLoading(false);
    }
  };

  const joinChallenge = async (challengeId) => {
    try {
      const response = await axios.post(`/challenges/${challengeId}/join`);
      toast.success(response.data.message);

      // Update challenges list
      setChallenges(challenges.map(challenge =>
        challenge._id === challengeId
          ? { ...challenge, userParticipating: true, statistics: { ...challenge.statistics, totalParticipants: response.data.participantCount } }
          : challenge
      ));
    } catch (error) {
      console.error('Error joining challenge:', error);
      toast.error(error.response?.data?.message || 'Failed to join challenge');
    }
  };

  const leaveChallenge = async (challengeId) => {
    try {
      const response = await axios.post(`/challenges/${challengeId}/leave`);
      toast.success(response.data.message);

      // Update challenges list
      setChallenges(challenges.map(challenge =>
        challenge._id === challengeId
          ? { ...challenge, userParticipating: false, statistics: { ...challenge.statistics, totalParticipants: response.data.participantCount } }
          : challenge
      ));

      // Also update user challenges if on that tab
      if (activeTab === 'my-challenges') {
        setUserChallenges(userChallenges.filter(challenge => challenge._id !== challengeId));
      }
    } catch (error) {
      console.error('Error leaving challenge:', error);
      toast.error(error.response?.data?.message || 'Failed to leave challenge');
    }
  };

  const viewChallengeDetails = async (challengeId) => {
    try {
      const response = await axios.get(`/challenges/${challengeId}`);
      setSelectedChallenge(response.data.challenge);
    } catch (error) {
      console.error('Error fetching challenge details:', error);
      toast.error('Failed to load challenge details');
    }
  };

  const displayChallenges = activeTab === 'my-challenges' ? userChallenges : challenges;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Challenges 🏆</h1>
            <p className="text-gray-600">Join eco-friendly challenges and compete with the community</p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn-eco flex items-center space-x-2"
          >
            <FaPlus />
            <span>Create Challenge</span>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'bg-green-500 text-white'
                  : 'bg-white text-gray-700 hover:bg-green-50'
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Category Filter */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex items-center space-x-2">
            <FaFilter className="text-gray-500" />
            <span className="text-gray-700 font-medium">Category:</span>
          </div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="input-field"
          >
            {categories.map(category => (
              <option key={category.value} value={category.value}>
                {category.icon} {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Challenges Grid */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {displayChallenges.map((challenge) => (
                <ChallengeCard
                  key={challenge._id}
                  challenge={challenge}
                  onJoin={joinChallenge}
                  onLeave={leaveChallenge}
                  onViewDetails={viewChallengeDetails}
                  isUserChallenge={activeTab === 'my-challenges'}
                />
              ))}
            </AnimatePresence>
          </div>
        )}

        {displayChallenges.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏆</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No challenges found</h3>
            <p className="text-gray-600 mb-4">
              {activeTab === 'my-challenges'
                ? "You haven't joined any challenges yet. Join some challenges to get started!"
                : "No challenges available in this category. Create your own challenge!"
              }
            </p>
            <button
              onClick={() => setShowCreateForm(true)}
              className="btn-eco"
            >
              Create New Challenge
            </button>
          </div>
        )}
      </div>

      {/* Create Challenge Modal */}
      <CreateChallengeModal
        isOpen={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onChallengeCreated={fetchChallenges}
      />

      {/* Challenge Details Modal */}
      <ChallengeDetailsModal
        challenge={selectedChallenge}
        onClose={() => setSelectedChallenge(null)}
        onJoin={joinChallenge}
        onLeave={leaveChallenge}
      />
    </div>
  );
};

// Challenge Card Component
const ChallengeCard = ({ challenge, onJoin, onLeave, onViewDetails, isUserChallenge }) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category) => {
    const icons = {
      transportation: '🚌',
      energy: '💡',
      water: '💧',
      waste: '♻️',
      food: '🥗',
      lifestyle: '🌱',
      community: '👥'
    };
    return icons[category] || '🌍';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="card-eco cursor-pointer"
      onClick={() => onViewDetails(challenge._id)}
    >
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-2xl">{getCategoryIcon(challenge.category)}</span>
          <span className={`badge ${getDifficultyColor(challenge.difficulty)}`}>
            {challenge.difficulty}
          </span>
        </div>
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <FaUsers />
          <span>{challenge.statistics?.totalParticipants || 0}</span>
        </div>
      </div>

      <h3 className="font-bold text-gray-900 mb-2">{challenge.title}</h3>
      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{challenge.description}</p>

      <div className="space-y-2 mb-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Duration:</span>
          <span className="font-medium">{challenge.duration} days</span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Days remaining:</span>
          <span className="font-medium text-orange-600">{challenge.daysRemaining}</span>
        </div>

        {isUserChallenge && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Progress:</span>
              <span className="font-medium">{challenge.userProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${challenge.userProgress}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
        <button
          onClick={() => onViewDetails(challenge._id)}
          className="btn-secondary flex-1 flex items-center justify-center space-x-1"
        >
          <FaEye />
          <span>View</span>
        </button>

        {challenge.userParticipating ? (
          <button
            onClick={() => onLeave(challenge._id)}
            className="btn-secondary flex-1"
          >
            Leave
          </button>
        ) : (
          <button
            onClick={() => onJoin(challenge._id)}
            className="btn-eco flex-1"
          >
            Join
          </button>
        )}
      </div>

      {challenge.userCompleted && (
        <div className="mt-3 p-2 bg-green-50 rounded-lg">
          <div className="flex items-center space-x-2 text-green-700">
            <FaTrophy />
            <span className="text-sm font-medium">Challenge Completed! 🎉</span>
          </div>
        </div>
      )}
    </motion.div>
  );
};

// Create Challenge Modal Component
const CreateChallengeModal = ({ isOpen, onClose, onChallengeCreated }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'lifestyle',
    duration: 7,
    startDate: new Date().toISOString().split('T')[0],
    difficulty: 'medium',
    habits: []
  });
  const [availableHabits, setAvailableHabits] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchAvailableHabits();
    }
  }, [isOpen]);

  const fetchAvailableHabits = async () => {
    try {
      const response = await axios.get('/habits/user');
      setAvailableHabits(response.data.habits);
    } catch (error) {
      console.error('Error fetching habits:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      await axios.post('/challenges', formData);
      toast.success('Challenge created successfully!');
      onChallengeCreated();
      onClose();
      setFormData({
        title: '',
        description: '',
        category: 'lifestyle',
        duration: 7,
        startDate: new Date().toISOString().split('T')[0],
        difficulty: 'medium',
        habits: []
      });
    } catch (error) {
      console.error('Error creating challenge:', error);
      toast.error(error.response?.data?.message || 'Failed to create challenge');
    } finally {
      setLoading(false);
    }
  };

  const toggleHabit = (habitId) => {
    setFormData(prev => ({
      ...prev,
      habits: prev.habits.includes(habitId)
        ? prev.habits.filter(id => id !== habitId)
        : [...prev.habits, habitId]
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">Create New Challenge</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Challenge Title
            </label>
            <input
              type="text"
              required
              className="input-field"
              value={formData.title}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              placeholder="e.g., 30-Day Zero Waste Challenge"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              required
              className="input-field"
              rows="3"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Describe your challenge..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                className="input-field"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
              >
                <option value="transportation">Transportation</option>
                <option value="energy">Energy</option>
                <option value="water">Water</option>
                <option value="waste">Waste</option>
                <option value="food">Food</option>
                <option value="lifestyle">Lifestyle</option>
                <option value="community">Community</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Difficulty
              </label>
              <select
                className="input-field"
                value={formData.difficulty}
                onChange={(e) => setFormData({...formData, difficulty: e.target.value})}
              >
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration (days)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                className="input-field"
                value={formData.duration}
                onChange={(e) => setFormData({...formData, duration: parseInt(e.target.value)})}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                className="input-field"
                value={formData.startDate}
                onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Habits (choose at least 1)
            </label>
            <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3">
              {availableHabits.map(habit => (
                <label key={habit._id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded">
                  <input
                    type="checkbox"
                    checked={formData.habits.includes(habit._id)}
                    onChange={() => toggleHabit(habit._id)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <span className="text-xl">{habit.icon}</span>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{habit.name}</div>
                    <div className="text-sm text-gray-600">{habit.category} • {habit.points} pts</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || formData.habits.length === 0}
              className="btn-eco flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create Challenge'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

// Challenge Details Modal Component
const ChallengeDetailsModal = ({ challenge, onClose, onJoin, onLeave }) => {
  if (!challenge) return null;

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{challenge.title}</h2>
            <div className="flex items-center space-x-4">
              <span className={`badge ${getDifficultyColor(challenge.difficulty)}`}>
                {challenge.difficulty}
              </span>
              <span className="text-gray-600">{challenge.category}</span>
              <span className="flex items-center space-x-1 text-gray-600">
                <FaUsers />
                <span>{challenge.statistics?.totalParticipants || 0} participants</span>
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Challenge Details</h3>
            <p className="text-gray-600 mb-4">{challenge.description}</p>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium">{challenge.duration} days</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Start Date:</span>
                <span className="font-medium">{new Date(challenge.startDate).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">End Date:</span>
                <span className="font-medium">{new Date(challenge.endDate).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Days Remaining:</span>
                <span className="font-medium text-orange-600">{challenge.daysRemaining}</span>
              </div>
            </div>

            {challenge.userParticipating && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">Your Progress</h4>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-green-700">Progress:</span>
                  <span className="font-medium text-green-900">{challenge.userProgress}%</span>
                </div>
                <div className="w-full bg-green-200 rounded-full h-3">
                  <div
                    className="bg-green-500 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${challenge.userProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Challenge Habits</h3>
            <div className="space-y-2">
              {challenge.habits?.map(habit => (
                <div key={habit._id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <span className="text-xl">{habit.icon}</span>
                  <div>
                    <div className="font-medium text-gray-900">{habit.name}</div>
                    <div className="text-sm text-gray-600">{habit.category} • {habit.points} pts</div>
                  </div>
                </div>
              ))}
            </div>

            {challenge.leaderboard && challenge.leaderboard.length > 0 && (
              <div className="mt-6">
                <h3 className="font-semibold text-gray-900 mb-3">Leaderboard</h3>
                <div className="space-y-2">
                  {challenge.leaderboard.slice(0, 5).map((participant, index) => (
                    <div key={participant.user._id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-400' :
                          index === 2 ? 'bg-orange-500' : 'bg-gray-600'
                        }`}>
                          {participant.rank}
                        </div>
                        <span className="font-medium">{participant.user.name}</span>
                        {participant.isCurrentUser && <span className="text-green-600 text-sm">(You)</span>}
                      </div>
                      <span className="font-medium">{participant.progress}%</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex space-x-3 mt-6 pt-6 border-t">
          <button
            onClick={onClose}
            className="btn-secondary flex-1"
          >
            Close
          </button>

          {challenge.userParticipating ? (
            <button
              onClick={() => {
                onLeave(challenge._id);
                onClose();
              }}
              className="btn-secondary flex-1"
            >
              Leave Challenge
            </button>
          ) : (
            <button
              onClick={() => {
                onJoin(challenge._id);
                onClose();
              }}
              className="btn-eco flex-1"
            >
              Join Challenge
            </button>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default Challenges;
