import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';

const PageTransition = ({ children }) => {
  const location = useLocation();

  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20,
      scale: 0.98
    },
    in: {
      opacity: 1,
      y: 0,
      scale: 1
    },
    out: {
      opacity: 0,
      y: -20,
      scale: 1.02
    }
  };

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.4
  };

  const slideVariants = {
    initial: {
      x: '100%',
      opacity: 0
    },
    in: {
      x: 0,
      opacity: 1
    },
    out: {
      x: '-100%',
      opacity: 0
    }
  };

  const slideTransition = {
    type: "tween",
    ease: "easeInOut",
    duration: 0.5
  };

  const scaleVariants = {
    initial: {
      scale: 0.8,
      opacity: 0,
      rotateY: -15
    },
    in: {
      scale: 1,
      opacity: 1,
      rotateY: 0
    },
    out: {
      scale: 1.1,
      opacity: 0,
      rotateY: 15
    }
  };

  const scaleTransition = {
    type: "spring",
    damping: 20,
    stiffness: 100,
    duration: 0.6
  };

  // Choose transition based on route
  const getTransitionConfig = (pathname) => {
    if (pathname === '/') {
      return { variants: scaleVariants, transition: scaleTransition };
    } else if (pathname.includes('/dashboard') || pathname.includes('/habits')) {
      return { variants: slideVariants, transition: slideTransition };
    } else {
      return { variants: pageVariants, transition: pageTransition };
    }
  };

  const { variants, transition } = getTransitionConfig(location.pathname);

  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={location.pathname}
        initial="initial"
        animate="in"
        exit="out"
        variants={variants}
        transition={transition}
        className="w-full"
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

// Stagger container for animating lists
export const StaggerContainer = ({ children, className = "", delay = 0.1 }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: delay,
        delayChildren: 0.1
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children}
    </motion.div>
  );
};

// Individual item for stagger animations
export const StaggerItem = ({ children, className = "" }) => {
  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 100
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={itemVariants}
    >
      {children}
    </motion.div>
  );
};

// Scroll-triggered animation
export const ScrollReveal = ({ 
  children, 
  className = "", 
  direction = "up", 
  delay = 0,
  threshold = 0.1 
}) => {
  const directions = {
    up: { y: 50 },
    down: { y: -50 },
    left: { x: 50 },
    right: { x: -50 }
  };

  const variants = {
    hidden: {
      opacity: 0,
      ...directions[direction]
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.6,
        delay,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: threshold }}
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

// Hover scale animation
export const HoverScale = ({ 
  children, 
  className = "", 
  scale = 1.05, 
  duration = 0.2 
}) => {
  return (
    <motion.div
      className={className}
      whileHover={{ 
        scale,
        transition: { duration, ease: "easeOut" }
      }}
      whileTap={{ scale: 0.95 }}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;
