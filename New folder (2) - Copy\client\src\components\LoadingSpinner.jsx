import React from 'react';
import { motion } from 'framer-motion';
import { FaLeaf } from 'react-icons/fa';

const LoadingSpinner = ({ size = 'md', variant = 'default', text = 'Loading...' }) => {
  const sizes = {
    sm: 'w-6 h-6',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const SpinnerDefault = () => (
    <motion.div
      className={`${sizes[size]} border-4 border-gray-200 border-t-green-500 rounded-full`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  );

  const SpinnerDots = () => (
    <div className="flex space-x-2">
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className="w-3 h-3 bg-green-500 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2
          }}
        />
      ))}
    </div>
  );

  const SpinnerPulse = () => (
    <motion.div
      className={`${sizes[size]} bg-green-500 rounded-full`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.7, 1, 0.7]
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );

  const SpinnerLeaf = () => (
    <motion.div
      className={`${sizes[size]} flex items-center justify-center text-green-500`}
      animate={{
        rotate: [0, 360],
        scale: [1, 1.1, 1]
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      <FaLeaf className="w-full h-full" />
    </motion.div>
  );

  const SpinnerWave = () => (
    <div className="flex space-x-1">
      {[0, 1, 2, 3, 4].map((i) => (
        <motion.div
          key={i}
          className="w-2 h-8 bg-green-500 rounded-full"
          animate={{
            scaleY: [1, 2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.1
          }}
        />
      ))}
    </div>
  );

  const SpinnerOrbit = () => (
    <div className={`relative ${sizes[size]}`}>
      <motion.div
        className="absolute inset-0 border-2 border-green-200 rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="absolute top-0 left-1/2 w-3 h-3 bg-green-500 rounded-full transform -translate-x-1/2 -translate-y-1/2"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        style={{ transformOrigin: `0 ${sizes[size].split(' ')[0].replace('w-', '').replace('[', '').replace(']', '') === '6' ? '1.5rem' : sizes[size].split(' ')[0].replace('w-', '').replace('[', '').replace(']', '') === '12' ? '3rem' : sizes[size].split(' ')[0].replace('w-', '').replace('[', '').replace(']', '') === '16' ? '4rem' : '6rem'}` }}
      />
    </div>
  );

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return <SpinnerDots />;
      case 'pulse':
        return <SpinnerPulse />;
      case 'leaf':
        return <SpinnerLeaf />;
      case 'wave':
        return <SpinnerWave />;
      case 'orbit':
        return <SpinnerOrbit />;
      default:
        return <SpinnerDefault />;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      {renderSpinner()}
      {text && (
        <motion.p
          className={`${textSizes[size]} text-gray-600 font-medium`}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

// Full screen loading overlay
export const LoadingOverlay = ({ isLoading, children, ...props }) => {
  if (!isLoading) return children;

  return (
    <div className="relative">
      {children}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <LoadingSpinner {...props} />
      </motion.div>
    </div>
  );
};

export default LoadingSpinner;
