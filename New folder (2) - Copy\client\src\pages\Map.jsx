import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaMapMarkerAlt, FaPlus, FaFilter, FaSearch, FaStar, FaHeart, FaEye, FaDirections } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import RealTimeMap from '../components/RealTimeMap';
import axios from 'axios';
import toast from 'react-hot-toast';

const Map = () => {
  const [places, setPlaces] = useState([]);
  const [filteredPlaces, setFilteredPlaces] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [placeTypes, setPlaceTypes] = useState([]);
  const { user } = useAuth();
  const { t } = useLanguage();

  useEffect(() => {
    fetchPlaces();
    fetchPlaceTypes();
    getUserLocation();
  }, []);

  useEffect(() => {
    filterPlaces();
  }, [places, selectedType, searchQuery]);

  const fetchPlaces = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedType !== 'all') params.append('type', selectedType);
      if (userLocation) {
        params.append('lat', userLocation.lat);
        params.append('lng', userLocation.lng);
      }

      const response = await axios.get(`/map/places?${params}`);
      setPlaces(response.data.places);
    } catch (error) {
      console.error('Error fetching places:', error);
      toast.error('Failed to load eco-friendly places');
    } finally {
      setLoading(false);
    }
  };

  const fetchPlaceTypes = async () => {
    try {
      const response = await axios.get('/map/types');
      setPlaceTypes(response.data.types);
    } catch (error) {
      console.error('Error fetching place types:', error);
    }
  };

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.log('Location access denied:', error);
        }
      );
    }
  };

  const filterPlaces = () => {
    let filtered = places;

    if (selectedType !== 'all') {
      filtered = filtered.filter(place => place.type === selectedType);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(place =>
        place.name.toLowerCase().includes(query) ||
        place.description.toLowerCase().includes(query) ||
        place.address.formatted.toLowerCase().includes(query)
      );
    }

    setFilteredPlaces(filtered);
  };

  const toggleFavorite = async (placeId) => {
    try {
      const response = await axios.post(`/map/places/${placeId}/favorite`);
      toast.success(response.data.message);

      // Update places list
      setPlaces(places.map(place =>
        place._id === placeId
          ? { ...place, isFavorite: response.data.isFavorite }
          : place
      ));
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error('Failed to update favorite');
    }
  };

  const viewPlaceDetails = async (placeId) => {
    try {
      const response = await axios.get(`/map/places/${placeId}`);
      setSelectedPlace(response.data.place);
    } catch (error) {
      console.error('Error fetching place details:', error);
      toast.error('Failed to load place details');
    }
  };

  const getDirections = (place) => {
    if (userLocation) {
      const url = `https://www.google.com/maps/dir/${userLocation.lat},${userLocation.lng}/${place.location.coordinates[1]},${place.location.coordinates[0]}`;
      window.open(url, '_blank');
    } else {
      const url = `https://www.google.com/maps/search/${encodeURIComponent(place.address.formatted)}`;
      window.open(url, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Eco Map 🗺️</h1>
            <p className="text-gray-600">Discover eco-friendly places near you</p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-eco flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Place</span>
          </button>
        </div>

        {/* Search and Filters */}
        <div className="grid md:grid-cols-3 gap-4 mb-8">
          <div className="md:col-span-2">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search places, descriptions, or addresses..."
                className="input-field pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="input-field"
            >
              {placeTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.icon} {type.label} ({type.count})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Real-Time Interactive Map */}
        <RealTimeMap
          onPlaceClick={viewPlaceDetails}
          height="600px"
        />

        {/* Places List */}
        {loading ? (
          <div className="flex items-center justify-center h-64 mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
          </div>
        ) : (
          <div className="mt-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              📍 Nearby Eco-Friendly Places
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatePresence>
                {filteredPlaces.map((place) => (
                  <PlaceCard
                    key={place._id}
                    place={place}
                    onToggleFavorite={toggleFavorite}
                    onViewDetails={viewPlaceDetails}
                    onGetDirections={getDirections}
                  />
                ))}
              </AnimatePresence>
            </div>
          </div>
        )}

        {filteredPlaces.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🗺️</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No places found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery
                ? `No places match "${searchQuery}". Try a different search term.`
                : selectedType !== 'all'
                ? `No ${selectedType.replace('_', ' ')} places found in your area.`
                : "No eco-friendly places found. Be the first to add one!"
              }
            </p>
            <button
              onClick={() => setShowAddForm(true)}
              className="btn-eco"
            >
              Add First Place
            </button>
          </div>
        )}
      </div>

      {/* Add Place Modal */}
      <AddPlaceModal
        isOpen={showAddForm}
        onClose={() => setShowAddForm(false)}
        onPlaceAdded={fetchPlaces}
        userLocation={userLocation}
      />

      {/* Place Details Modal */}
      <PlaceDetailsModal
        place={selectedPlace}
        onClose={() => setSelectedPlace(null)}
        onToggleFavorite={toggleFavorite}
        onGetDirections={getDirections}
      />
    </div>
  );
};

// Place Card Component
const PlaceCard = ({ place, onToggleFavorite, onViewDetails, onGetDirections }) => {
  const getTypeIcon = (type) => {
    const icons = {
      recycling_center: '♻️',
      organic_shop: '🥬',
      ev_charging: '🔌',
      renewable_energy: '☀️',
      sustainable_business: '🏪',
      community_garden: '🌱',
      farmers_market: '🥕',
      repair_cafe: '🔧',
      zero_waste_store: '🌿',
      composting_facility: '🍂'
    };
    return icons[type] || '🌍';
  };

  const getTypeLabel = (type) => {
    const labels = {
      recycling_center: 'Recycling Center',
      organic_shop: 'Organic Shop',
      ev_charging: 'EV Charging',
      renewable_energy: 'Renewable Energy',
      sustainable_business: 'Sustainable Business',
      community_garden: 'Community Garden',
      farmers_market: 'Farmers Market',
      repair_cafe: 'Repair Cafe',
      zero_waste_store: 'Zero Waste Store',
      composting_facility: 'Composting Facility'
    };
    return labels[type] || type.replace('_', ' ');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="card-eco cursor-pointer"
      onClick={() => onViewDetails(place._id)}
    >
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-2xl">{getTypeIcon(place.type)}</span>
          <span className="badge badge-info">{getTypeLabel(place.type)}</span>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(place._id);
          }}
          className={`p-2 rounded-full transition-colors ${
            place.isFavorite
              ? 'text-red-500 hover:text-red-600'
              : 'text-gray-400 hover:text-red-500'
          }`}
        >
          <FaHeart />
        </button>
      </div>

      <h3 className="font-bold text-gray-900 mb-2">{place.name}</h3>
      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{place.description}</p>

      <div className="space-y-2 mb-4">
        <div className="flex items-center text-sm text-gray-600">
          <FaMapMarkerAlt className="mr-2" />
          <span className="line-clamp-1">{place.address.formatted}</span>
        </div>

        {place.rating && (
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <FaStar
                  key={i}
                  className={`text-sm ${
                    i < Math.floor(place.rating.average)
                      ? 'text-yellow-400'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-600">
              {place.rating.average.toFixed(1)} ({place.rating.count} reviews)
            </span>
          </div>
        )}

        {place.distance && (
          <div className="text-sm text-gray-600">
            📍 {place.distance.toFixed(1)} km away
          </div>
        )}
      </div>

      <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
        <button
          onClick={() => onViewDetails(place._id)}
          className="btn-secondary flex-1 flex items-center justify-center space-x-1"
        >
          <FaEye />
          <span>View</span>
        </button>

        <button
          onClick={() => onGetDirections(place)}
          className="btn-eco flex-1 flex items-center justify-center space-x-1"
        >
          <FaDirections />
          <span>Directions</span>
        </button>
      </div>

      {place.tags && place.tags.length > 0 && (
        <div className="mt-3 flex flex-wrap gap-1">
          {place.tags.slice(0, 3).map(tag => (
            <span key={tag} className="badge badge-secondary text-xs">
              {tag}
            </span>
          ))}
        </div>
      )}
    </motion.div>
  );
};

// Add Place Modal Component
const AddPlaceModal = ({ isOpen, onClose, onPlaceAdded, userLocation }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'sustainable_business',
    address: { formatted: '' },
    coordinates: userLocation ? [userLocation.lng, userLocation.lat] : [0, 0],
    contact: { phone: '', website: '', email: '' },
    hours: '',
    tags: []
  });
  const [loading, setLoading] = useState(false);
  const [tagInput, setTagInput] = useState('');

  const placeTypes = [
    { value: 'recycling_center', label: 'Recycling Center' },
    { value: 'organic_shop', label: 'Organic Shop' },
    { value: 'ev_charging', label: 'EV Charging Station' },
    { value: 'renewable_energy', label: 'Renewable Energy' },
    { value: 'sustainable_business', label: 'Sustainable Business' },
    { value: 'community_garden', label: 'Community Garden' },
    { value: 'farmers_market', label: 'Farmers Market' },
    { value: 'repair_cafe', label: 'Repair Cafe' },
    { value: 'zero_waste_store', label: 'Zero Waste Store' },
    { value: 'composting_facility', label: 'Composting Facility' }
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      await axios.post('/map/places', formData);
      toast.success('Place submitted successfully! It will be reviewed by our team.');
      onPlaceAdded();
      onClose();
      setFormData({
        name: '',
        description: '',
        type: 'sustainable_business',
        address: { formatted: '' },
        coordinates: userLocation ? [userLocation.lng, userLocation.lat] : [0, 0],
        contact: { phone: '', website: '', email: '' },
        hours: '',
        tags: []
      });
    } catch (error) {
      console.error('Error adding place:', error);
      toast.error(error.response?.data?.message || 'Failed to add place');
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">Add Eco-Friendly Place</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Place Name *
            </label>
            <input
              type="text"
              required
              className="input-field"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              placeholder="e.g., Green Earth Recycling Center"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type *
            </label>
            <select
              required
              className="input-field"
              value={formData.type}
              onChange={(e) => setFormData({...formData, type: e.target.value})}
            >
              {placeTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              required
              className="input-field"
              rows="3"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Describe what makes this place eco-friendly..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address *
            </label>
            <input
              type="text"
              required
              className="input-field"
              value={formData.address.formatted}
              onChange={(e) => setFormData({
                ...formData,
                address: { formatted: e.target.value }
              })}
              placeholder="Full address including city and state"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                type="tel"
                className="input-field"
                value={formData.contact.phone}
                onChange={(e) => setFormData({
                  ...formData,
                  contact: { ...formData.contact, phone: e.target.value }
                })}
                placeholder="+****************"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                className="input-field"
                value={formData.contact.website}
                onChange={(e) => setFormData({
                  ...formData,
                  contact: { ...formData.contact, website: e.target.value }
                })}
                placeholder="https://example.com"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Operating Hours
            </label>
            <input
              type="text"
              className="input-field"
              value={formData.hours}
              onChange={(e) => setFormData({...formData, hours: e.target.value})}
              placeholder="e.g., Mon-Fri: 9AM-6PM, Sat: 9AM-2PM"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>
            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                className="input-field flex-1"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                placeholder="Add tags (e.g., organic, solar-powered)"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <button
                type="button"
                onClick={addTag}
                className="btn-secondary"
              >
                Add
              </button>
            </div>
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map(tag => (
                  <span
                    key={tag}
                    className="badge badge-secondary flex items-center space-x-1"
                  >
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="text-red-500 hover:text-red-700"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-eco flex-1"
            >
              {loading ? 'Submitting...' : 'Submit Place'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

// Place Details Modal Component
const PlaceDetailsModal = ({ place, onClose, onToggleFavorite, onGetDirections }) => {
  const [review, setReview] = useState({ rating: 5, comment: '' });
  const [submittingReview, setSubmittingReview] = useState(false);

  const submitReview = async (e) => {
    e.preventDefault();
    try {
      setSubmittingReview(true);
      await axios.post(`/map/places/${place._id}/review`, review);
      toast.success('Review submitted successfully!');
      setReview({ rating: 5, comment: '' });
      // Refresh place details
      window.location.reload();
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error(error.response?.data?.message || 'Failed to submit review');
    } finally {
      setSubmittingReview(false);
    }
  };

  if (!place) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{place.name}</h2>
            <div className="flex items-center space-x-4">
              <span className="badge badge-info">{place.type.replace('_', ' ')}</span>
              {place.rating && (
                <div className="flex items-center space-x-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <FaStar
                        key={i}
                        className={`text-sm ${
                          i < Math.floor(place.rating.average)
                            ? 'text-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {place.rating.average.toFixed(1)} ({place.rating.count} reviews)
                  </span>
                </div>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">About</h3>
            <p className="text-gray-600 mb-4">{place.description}</p>

            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <FaMapMarkerAlt className="text-gray-500 mt-1" />
                <span className="text-gray-700">{place.address.formatted}</span>
              </div>

              {place.contact?.phone && (
                <div className="flex items-center space-x-2">
                  <span className="text-gray-500">📞</span>
                  <span className="text-gray-700">{place.contact.phone}</span>
                </div>
              )}

              {place.contact?.website && (
                <div className="flex items-center space-x-2">
                  <span className="text-gray-500">🌐</span>
                  <a
                    href={place.contact.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-green-600 hover:text-green-700"
                  >
                    Visit Website
                  </a>
                </div>
              )}

              {place.hours && (
                <div className="flex items-start space-x-2">
                  <span className="text-gray-500 mt-1">🕒</span>
                  <span className="text-gray-700">{place.hours}</span>
                </div>
              )}
            </div>

            {place.tags && place.tags.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {place.tags.map(tag => (
                    <span key={tag} className="badge badge-secondary">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div>
            {/* Reviews Section */}
            <h3 className="font-semibold text-gray-900 mb-3">Reviews</h3>

            {!place.userReview && (
              <form onSubmit={submitReview} className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Write a Review</h4>
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Rating
                  </label>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map(rating => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => setReview(prev => ({ ...prev, rating }))}
                        className={`text-xl ${
                          rating <= review.rating ? 'text-yellow-400' : 'text-gray-300'
                        }`}
                      >
                        <FaStar />
                      </button>
                    ))}
                  </div>
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Comment (optional)
                  </label>
                  <textarea
                    className="input-field"
                    rows="3"
                    value={review.comment}
                    onChange={(e) => setReview(prev => ({ ...prev, comment: e.target.value }))}
                    placeholder="Share your experience..."
                  />
                </div>
                <button
                  type="submit"
                  disabled={submittingReview}
                  className="btn-eco"
                >
                  {submittingReview ? 'Submitting...' : 'Submit Review'}
                </button>
              </form>
            )}

            {place.reviews && place.reviews.length > 0 && (
              <div className="space-y-4">
                {place.reviews.slice(0, 3).map(review => (
                  <div key={review._id} className="border-b border-gray-200 pb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">{review.user.name}</span>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <FaStar
                            key={i}
                            className={`text-xs ${
                              i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    {review.comment && (
                      <p className="text-gray-600 text-sm">{review.comment}</p>
                    )}
                    <span className="text-xs text-gray-500">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="flex space-x-3 mt-6 pt-6 border-t">
          <button
            onClick={onClose}
            className="btn-secondary flex-1"
          >
            Close
          </button>

          <button
            onClick={() => onToggleFavorite(place._id)}
            className={`flex-1 px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${
              place.isFavorite
                ? 'bg-red-500 text-white hover:bg-red-600'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <FaHeart />
            <span>{place.isFavorite ? 'Remove from Favorites' : 'Add to Favorites'}</span>
          </button>

          <button
            onClick={() => onGetDirections(place)}
            className="btn-eco flex-1 flex items-center justify-center space-x-2"
          >
            <FaDirections />
            <span>Get Directions</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default Map;
