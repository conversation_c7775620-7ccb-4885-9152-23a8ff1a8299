import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaLeaf, 
  FaRecycle, 
  FaGlobe, 
  FaUsers, 
  FaTrophy, 
  FaMapMarkerAlt,
  FaRobot,
  FaChartLine
} from 'react-icons/fa';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';

const Home = () => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: <FaLeaf className="text-4xl text-green-500" />,
      title: "Daily Habit Tracker",
      description: "Track your eco-friendly habits and build sustainable routines"
    },
    {
      icon: <FaChartLine className="text-4xl text-blue-500" />,
      title: "Impact Dashboard",
      description: "See your environmental impact with CO₂, water, and energy savings"
    },
    {
      icon: <FaTrophy className="text-4xl text-yellow-500" />,
      title: "Eco Points & Rewards",
      description: "Earn points for consistency and unlock achievements"
    },
    {
      icon: <FaUsers className="text-4xl text-purple-500" />,
      title: "Community Challenges",
      description: "Join challenges and compete with other eco-warriors"
    },
    {
      icon: <FaMapMarkerAlt className="text-4xl text-red-500" />,
      title: "Eco-Friendly Places",
      description: "Find nearby recycling centers, organic shops, and EV stations"
    },
    {
      icon: <FaRobot className="text-4xl text-indigo-500" />,
      title: "AI Eco Assistant",
      description: "Get personalized eco-friendly tips and advice"
    }
  ];

  const stats = [
    { number: "10K+", label: "Active Users" },
    { number: "50K+", label: "Habits Completed" },
    { number: "2.5M", label: "kg CO₂ Saved" },
    { number: "1M+", label: "Liters Water Saved" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-emerald-900 via-green-800 to-teal-900 text-white overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-black/40 via-transparent to-black/20"></div>

          {/* Large decorative circles */}
          <motion.div
            className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />

          <motion.div
            className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-blue-400/20 to-cyan-500/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              rotate: [360, 180, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          />

          {/* Floating particles */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/30 rounded-full"
              animate={{
                y: [0, -120, 0],
                x: [0, Math.random() * 60 - 30, 0],
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 6 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: "easeInOut"
              }}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
        </div>

        <div className="relative max-w-7xl mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8"
            >
              <FaStar className="text-yellow-400" />
              <span className="text-sm font-medium">Join 10,000+ Eco Warriors</span>
            </motion.div>

            <motion.h1
              className="text-6xl md:text-8xl lg:text-9xl font-black mb-8 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              <span className="bg-gradient-to-r from-green-300 via-emerald-300 to-teal-300 bg-clip-text text-transparent">
                🌱 {t('welcomeTitle')}
              </span>
            </motion.h1>

            <motion.p
              className="text-xl md:text-3xl mb-12 text-green-100 max-w-5xl mx-auto leading-relaxed font-light"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              {t('welcomeSubtitle')}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              {!isAuthenticated ? (
                <>
                  <HoverScale scale={1.05}>
                    <Link
                      to="/register"
                      className="group relative overflow-hidden bg-gradient-to-r from-green-400 to-emerald-500 text-white font-bold text-lg px-12 py-4 rounded-2xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 inline-flex items-center space-x-3"
                    >
                      <span className="relative z-10">{t('getStarted')}</span>
                      <motion.div
                        className="relative z-10"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        🚀
                      </motion.div>
                      {/* Shine effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        initial={{ x: '-100%' }}
                        whileHover={{ x: '100%' }}
                        transition={{ duration: 0.6 }}
                      />
                    </Link>
                  </HoverScale>

                  <HoverScale scale={1.02}>
                    <Link
                      to="/login"
                      className="group bg-white/10 hover:bg-white/20 text-white font-bold py-4 px-12 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/20 inline-flex items-center space-x-2"
                    >
                      <FaPlay className="text-sm" />
                      <span>{t('login')}</span>
                    </Link>
                  </HoverScale>
                </>
              ) : (
                <HoverScale scale={1.05}>
                  <Link
                    to="/dashboard"
                    className="bg-gradient-to-r from-green-400 to-emerald-500 text-white font-bold text-lg px-12 py-4 rounded-2xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 inline-flex items-center space-x-3"
                  >
                    <span>Go to Dashboard</span>
                    <span>📊</span>
                  </Link>
                </HoverScale>
              )}
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              className="flex flex-wrap justify-center items-center gap-8 text-green-200/80"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.8 }}
            >
              <div className="flex items-center space-x-2">
                <FaUsers className="text-lg" />
                <span className="text-sm font-medium">10,000+ Users</span>
              </div>
              <div className="flex items-center space-x-2">
                <FaTrophy className="text-lg" />
                <span className="text-sm font-medium">Award Winning</span>
              </div>
              <div className="flex items-center space-x-2">
                <FaLeaf className="text-lg" />
                <span className="text-sm font-medium">100% Eco-Friendly</span>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Enhanced Floating elements */}
        <motion.div
          className="absolute top-20 left-10"
          animate={{
            y: [0, -30, 0],
            rotate: [0, 10, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
        >
          <FaLeaf className="text-8xl text-green-300/40" />
        </motion.div>

        <motion.div
          className="absolute bottom-20 right-10"
          animate={{
            y: [0, -25, 0],
            rotate: [0, -15, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        >
          <FaRecycle className="text-8xl text-blue-300/40" />
        </motion.div>

        <motion.div
          className="absolute top-1/2 left-20"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 360],
            scale: [1, 1.15, 1]
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 2 }}
        >
          <FaGlobe className="text-7xl text-yellow-300/40" />
        </motion.div>
      </section>

      {/* Stats Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 via-white to-green-50/30 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0">
          <motion.div
            className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-green-100/50 to-emerald-100/50 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          <motion.div
            className="absolute bottom-10 left-10 w-48 h-48 bg-gradient-to-tr from-blue-100/50 to-cyan-100/50 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              rotate: [360, 180, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>

        <div className="relative max-w-7xl mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-16">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-6 py-3 rounded-full mb-6 font-semibold"
              >
                <FaStar className="text-green-600" />
                <span>Our Impact Together</span>
              </motion.div>

              <h2 className="text-5xl md:text-6xl font-black text-gray-900 mb-6">
                Making a Real <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Difference</span> 🌍
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Join a growing community of eco-warriors creating positive environmental change
              </p>
            </div>
          </ScrollReveal>

          <StaggerContainer className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <StaggerItem key={index}>
                <motion.div
                  className="relative group"
                  whileHover={{ y: -10, scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Card */}
                  <div className="bg-white/80 backdrop-blur-lg rounded-3xl p-8 text-center shadow-xl border border-white/20 group-hover:shadow-2xl transition-all duration-500">
                    {/* Animated background gradient */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-400/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                      initial={false}
                    />

                    {/* Number with counter animation */}
                    <motion.div
                      className="relative z-10 text-5xl md:text-6xl font-black mb-4"
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{
                        type: "spring",
                        stiffness: 100,
                        delay: index * 0.1
                      }}
                    >
                      <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                        {stat.number}
                      </span>
                    </motion.div>

                    {/* Label */}
                    <div className="relative z-10 text-gray-700 font-semibold text-lg group-hover:text-green-600 transition-colors">
                      {stat.label}
                    </div>

                    {/* Animated progress bar */}
                    <motion.div
                      className="mt-6 h-2 bg-gray-200 rounded-full overflow-hidden relative z-10"
                      initial={{ width: 0 }}
                      whileInView={{ width: "100%" }}
                      transition={{ duration: 0.5, delay: index * 0.1 + 1 }}
                    >
                      <motion.div
                        className="h-full bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: "100%" }}
                        transition={{ duration: 1.5, delay: index * 0.1 + 1.2 }}
                      />
                    </motion.div>

                    {/* Glow effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-3xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"
                      initial={false}
                    />
                  </div>
                </motion.div>
              </StaggerItem>
            ))}
          </StaggerContainer>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 bg-gradient-to-br from-white via-green-50/20 to-emerald-50/30 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0">
          <motion.div
            className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-br from-emerald-100/30 to-green-100/30 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.1, 1],
              x: [0, 50, 0],
              y: [0, -30, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-20 right-20 w-56 h-56 bg-gradient-to-tr from-teal-100/30 to-cyan-100/30 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              x: [0, -40, 0],
              y: [0, 20, 0],
            }}
            transition={{
              duration: 18,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="relative max-w-7xl mx-auto px-4">
          <ScrollReveal>
            <div className="text-center mb-20">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-6 py-3 rounded-full mb-6 font-semibold"
              >
                <FaStar className="text-green-600" />
                <span>Premium Features</span>
              </motion.div>

              <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-8">
                Why Choose <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">GreenMate</span>? 🌍
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Join thousands of eco-warriors making a positive impact on our planet through daily sustainable habits.
              </p>
            </div>
          </ScrollReveal>

          <StaggerContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
            {features.map((feature, index) => (
              <StaggerItem key={index}>
                <motion.div
                  className="group relative"
                  whileHover={{
                    y: -15,
                    rotateY: 5,
                    scale: 1.02
                  }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Card */}
                  <div className="bg-white/90 backdrop-blur-lg rounded-3xl p-8 text-center shadow-xl border border-white/20 group-hover:shadow-2xl transition-all duration-500 relative overflow-hidden">
                    {/* Animated background gradient */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                      initial={false}
                    />

                    {/* Icon with animation */}
                    <motion.div
                      className="mb-6 relative z-10"
                      whileHover={{
                        scale: 1.2,
                        rotate: [0, -10, 10, 0],
                      }}
                      transition={{ duration: 0.5 }}
                    >
                      <div className="w-20 h-20 mx-auto bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center text-white text-3xl shadow-lg group-hover:shadow-xl transition-shadow">
                        {feature.icon}
                      </div>
                    </motion.div>

                    <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors relative z-10">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed text-lg relative z-10">
                      {feature.description}
                    </p>

                    {/* Hover effect line */}
                    <motion.div
                      className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-green-400 to-emerald-400"
                      initial={{ width: 0 }}
                      whileHover={{ width: "100%" }}
                      transition={{ duration: 0.3 }}
                    />

                    {/* Glow effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-3xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"
                      initial={false}
                    />
                  </div>
                </motion.div>
              </StaggerItem>
            ))}
          </StaggerContainer>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-32 bg-gradient-to-br from-emerald-900 via-green-800 to-teal-900 text-white overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-black/40"></div>
          {/* Floating orbs */}
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-white/10"
              style={{
                width: `${Math.random() * 120 + 60}px`,
                height: `${Math.random() * 120 + 60}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -60, 0],
                x: [0, Math.random() * 40 - 20, 0],
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.7, 0.3],
              }}
              transition={{
                duration: 8 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}

          {/* Large gradient circles */}
          <motion.div
            className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />

          <motion.div
            className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-teal-400/20 to-cyan-500/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.4, 1],
              rotate: [360, 180, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>

        <div className="relative max-w-6xl mx-auto text-center px-4">
          <ScrollReveal>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 1 }}
            >
              {/* Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-8 py-4 mb-8"
              >
                <FaStar className="text-yellow-400" />
                <span className="text-lg font-semibold">Start Your Journey Today</span>
              </motion.div>

              <motion.h2
                className="text-5xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.8 }}
              >
                Ready to Make a <br />
                <span className="bg-gradient-to-r from-yellow-300 via-green-300 to-emerald-300 bg-clip-text text-transparent">
                  Difference?
                </span> 🌟
              </motion.h2>

              <motion.p
                className="text-xl md:text-2xl mb-12 text-green-100 max-w-4xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              >
                Start your eco-friendly journey today and be part of the solution for a sustainable future.
              </motion.p>

              {!isAuthenticated && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8, duration: 0.8 }}
                >
                  <HoverScale scale={1.1}>
                    <Link
                      to="/register"
                      className="group relative overflow-hidden inline-flex items-center space-x-4 bg-white text-green-600 hover:bg-green-50 font-black py-6 px-12 rounded-2xl text-xl transition-all duration-300 shadow-2xl hover:shadow-white/20"
                    >
                      <span className="relative z-10">Join GreenMate Now!</span>
                      <motion.div
                        className="relative z-10"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        🚀
                      </motion.div>
                      {/* Shine effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-green-200/30 to-transparent"
                        initial={{ x: '-100%' }}
                        whileHover={{ x: '100%' }}
                        transition={{ duration: 0.6 }}
                      />
                    </Link>
                  </HoverScale>
                </motion.div>
              )}

              {/* Trust indicators */}
              <motion.div
                className="mt-16 flex flex-wrap justify-center items-center gap-8 text-green-200"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                <div className="flex items-center space-x-2">
                  <FaUsers />
                  <span className="text-sm font-medium">10,000+ Users</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaTrophy />
                  <span className="text-sm font-medium">Award Winning</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaLeaf />
                  <span className="text-sm font-medium">100% Eco-Friendly</span>
                </div>
              </motion.div>
            </motion.div>
          </ScrollReveal>
        </div>
      </section>
    </div>
  );
};

export default Home;
