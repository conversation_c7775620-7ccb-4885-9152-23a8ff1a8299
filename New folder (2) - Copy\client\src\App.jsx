import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { HabitProvider } from './contexts/HabitContext';
import { LanguageProvider } from './contexts/LanguageContext';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import PageTransition from './components/PageTransition';
import AnimatedBackground from './components/AnimatedBackground';
import ScrollProgress, { ScrollToTop } from './components/ScrollProgress';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import Habits from './pages/Habits';
import Impact from './pages/Impact';
import Community from './pages/Community';
import Challenges from './pages/Challenges';
import Map from './pages/Map';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import AIAssistant from './pages/AIAssistant';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <HabitProvider>
          <Router>
            <AnimatedBackground>
              <div className="min-h-screen flex flex-col">
                <ScrollProgress />
                <Navbar />

                <main className="flex-1">
                  <PageTransition>
                    <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<Home />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />

                  {/* Protected Routes */}
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  } />

                  <Route path="/habits" element={
                    <ProtectedRoute>
                      <Habits />
                    </ProtectedRoute>
                  } />

                  <Route path="/impact" element={
                    <ProtectedRoute>
                      <Impact />
                    </ProtectedRoute>
                  } />

                  <Route path="/community" element={
                    <ProtectedRoute>
                      <Community />
                    </ProtectedRoute>
                  } />

                  <Route path="/challenges" element={
                    <ProtectedRoute>
                      <Challenges />
                    </ProtectedRoute>
                  } />

                  <Route path="/map" element={
                    <ProtectedRoute>
                      <Map />
                    </ProtectedRoute>
                  } />

                  <Route path="/ai-assistant" element={
                    <ProtectedRoute>
                      <AIAssistant />
                    </ProtectedRoute>
                  } />

                  <Route path="/profile" element={
                    <ProtectedRoute>
                      <Profile />
                    </ProtectedRoute>
                  } />

                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <Settings />
                    </ProtectedRoute>
                  } />

                      {/* Catch all route */}
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </PageTransition>
                </main>

                <Footer />

                {/* Toast notifications */}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: 'rgba(54, 54, 54, 0.95)',
                      color: '#fff',
                      backdropFilter: 'blur(10px)',
                      borderRadius: '12px',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                    },
                    success: {
                      duration: 3000,
                      iconTheme: {
                        primary: '#22c55e',
                        secondary: '#fff',
                      },
                    },
                    error: {
                      duration: 4000,
                      iconTheme: {
                        primary: '#ef4444',
                        secondary: '#fff',
                      },
                    },
                  }}
                />
                <ScrollToTop />
              </div>
            </AnimatedBackground>
          </Router>
        </HabitProvider>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;
