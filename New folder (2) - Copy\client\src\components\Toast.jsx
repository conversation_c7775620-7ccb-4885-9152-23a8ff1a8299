import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCheck, FaTimes, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';

const Toast = ({ 
  message, 
  type = 'success', 
  isVisible, 
  onClose, 
  duration = 4000,
  position = 'top-right' 
}) => {
  const [isShowing, setIsShowing] = React.useState(isVisible);

  React.useEffect(() => {
    setIsShowing(isVisible);
    
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        setIsShowing(false);
        setTimeout(onClose, 300); // Wait for animation to complete
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  const positions = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  };

  const types = {
    success: {
      bg: 'bg-gradient-to-r from-green-500 to-emerald-500',
      icon: FaCheck,
      iconBg: 'bg-green-600'
    },
    error: {
      bg: 'bg-gradient-to-r from-red-500 to-pink-500',
      icon: FaTimes,
      iconBg: 'bg-red-600'
    },
    warning: {
      bg: 'bg-gradient-to-r from-yellow-500 to-orange-500',
      icon: FaExclamationTriangle,
      iconBg: 'bg-yellow-600'
    },
    info: {
      bg: 'bg-gradient-to-r from-blue-500 to-indigo-500',
      icon: FaInfoCircle,
      iconBg: 'bg-blue-600'
    }
  };

  const config = types[type];
  const IconComponent = config.icon;

  const toastVariants = {
    initial: {
      opacity: 0,
      scale: 0.8,
      y: position.includes('top') ? -50 : 50,
      x: position.includes('right') ? 50 : position.includes('left') ? -50 : 0
    },
    animate: {
      opacity: 1,
      scale: 1,
      y: 0,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: position.includes('top') ? -50 : 50,
      x: position.includes('right') ? 50 : position.includes('left') ? -50 : 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  return (
    <AnimatePresence>
      {isShowing && (
        <motion.div
          className={`fixed ${positions[position]} z-50 max-w-sm w-full`}
          variants={toastVariants}
          initial="initial"
          animate="animate"
          exit="exit"
        >
          <motion.div
            className={`${config.bg} text-white rounded-xl shadow-2xl overflow-hidden`}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400 }}
          >
            {/* Progress bar */}
            {duration > 0 && (
              <motion.div
                className="h-1 bg-white/30"
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: duration / 1000, ease: "linear" }}
              />
            )}
            
            <div className="p-4">
              <div className="flex items-start space-x-3">
                {/* Icon */}
                <motion.div
                  className={`${config.iconBg} rounded-full p-2 flex-shrink-0`}
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 500 }}
                >
                  <IconComponent className="w-4 h-4 text-white" />
                </motion.div>
                
                {/* Message */}
                <div className="flex-1 min-w-0">
                  <motion.p
                    className="text-sm font-medium leading-relaxed"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    {message}
                  </motion.p>
                </div>
                
                {/* Close button */}
                <motion.button
                  onClick={() => {
                    setIsShowing(false);
                    setTimeout(onClose, 300);
                  }}
                  className="flex-shrink-0 text-white/80 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <FaTimes className="w-3 h-3" />
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Toast container for managing multiple toasts
export const ToastContainer = ({ toasts, removeToast, position = 'top-right' }) => {
  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      <AnimatePresence>
        {toasts.map((toast, index) => (
          <motion.div
            key={toast.id}
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: 1,
              y: index * 80 // Stack toasts
            }}
            exit={{ opacity: 0 }}
            className="pointer-events-auto"
          >
            <Toast
              {...toast}
              position={position}
              onClose={() => removeToast(toast.id)}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

// Hook for managing toasts
export const useToast = () => {
  const [toasts, setToasts] = React.useState([]);

  const addToast = (message, type = 'success', options = {}) => {
    const id = Date.now() + Math.random();
    const toast = {
      id,
      message,
      type,
      isVisible: true,
      ...options
    };
    
    setToasts(prev => [...prev, toast]);
    
    return id;
  };

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const success = (message, options) => addToast(message, 'success', options);
  const error = (message, options) => addToast(message, 'error', options);
  const warning = (message, options) => addToast(message, 'warning', options);
  const info = (message, options) => addToast(message, 'info', options);

  return {
    toasts,
    addToast,
    removeToast,
    success,
    error,
    warning,
    info
  };
};

export default Toast;
