const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  avatar: {
    type: String,
    default: ''
  },
  bio: {
    type: String,
    maxlength: [200, 'Bio cannot exceed 200 characters'],
    default: ''
  },
  location: {
    city: String,
    country: String,
    coordinates: {
      lat: Number,
      lng: Number
    }
  },
  preferences: {
    language: {
      type: String,
      enum: ['en', 'ta', 'tanglish'],
      default: 'en'
    },
    notifications: {
      daily: { type: Boolean, default: true },
      weekly: { type: Boolean, default: true },
      challenges: { type: Boolean, default: true },
      achievements: { type: Boolean, default: true }
    },
    privacy: {
      profileVisible: { type: Boolean, default: true },
      statsVisible: { type: Boolean, default: true },
      leaderboardVisible: { type: Boolean, default: true }
    }
  },
  ecoProfile: {
    totalPoints: { type: Number, default: 0 },
    level: { type: Number, default: 1 },
    streak: { type: Number, default: 0 },
    longestStreak: { type: Number, default: 0 },
    badges: [{
      name: String,
      description: String,
      icon: String,
      earnedAt: { type: Date, default: Date.now }
    }],
    impact: {
      co2Saved: { type: Number, default: 0 }, // in kg
      waterSaved: { type: Number, default: 0 }, // in liters
      electricitySaved: { type: Number, default: 0 }, // in kWh
      plasticAvoided: { type: Number, default: 0 }, // in grams
      treesEquivalent: { type: Number, default: 0 }
    }
  },
  social: {
    following: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    followers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    friends: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
  },
  joinedChallenges: [{
    challengeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Challenge' },
    joinedAt: { type: Date, default: Date.now },
    completed: { type: Boolean, default: false },
    progress: { type: Number, default: 0 }
  }],
  lastActive: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  emailVerified: { type: Boolean, default: false },
  resetPasswordToken: String,
  resetPasswordExpires: Date
}, {
  timestamps: true
});

// Index for better query performance
userSchema.index({ email: 1 });
userSchema.index({ 'ecoProfile.totalPoints': -1 });
userSchema.index({ 'ecoProfile.level': -1 });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Calculate level based on points
userSchema.methods.calculateLevel = function() {
  const points = this.ecoProfile.totalPoints;
  // Level formula: level = floor(sqrt(points / 100)) + 1
  this.ecoProfile.level = Math.floor(Math.sqrt(points / 100)) + 1;
  return this.ecoProfile.level;
};

// Add points and update level
userSchema.methods.addPoints = function(points) {
  this.ecoProfile.totalPoints += points;
  this.calculateLevel();
  return this.ecoProfile.totalPoints;
};

// Update impact stats
userSchema.methods.updateImpact = function(impactData) {
  const impact = this.ecoProfile.impact;
  if (impactData.co2Saved) impact.co2Saved += impactData.co2Saved;
  if (impactData.waterSaved) impact.waterSaved += impactData.waterSaved;
  if (impactData.electricitySaved) impact.electricitySaved += impactData.electricitySaved;
  if (impactData.plasticAvoided) impact.plasticAvoided += impactData.plasticAvoided;
  
  // Calculate trees equivalent (1 tree = ~22 kg CO2 per year)
  impact.treesEquivalent = Math.round(impact.co2Saved / 22 * 100) / 100;
};

// Add badge
userSchema.methods.addBadge = function(badge) {
  const existingBadge = this.ecoProfile.badges.find(b => b.name === badge.name);
  if (!existingBadge) {
    this.ecoProfile.badges.push(badge);
    return true;
  }
  return false;
};

// Update last active
userSchema.methods.updateLastActive = function() {
  this.lastActive = new Date();
};

module.exports = mongoose.model('User', userSchema);
