import React from 'react';
import { motion } from 'framer-motion';

const AnimatedButton = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className = '', 
  onClick,
  disabled = false,
  loading = false,
  icon,
  ...props 
}) => {
  const variants = {
    primary: 'btn-eco',
    secondary: 'btn-secondary',
    outline: 'btn-outline',
    ghost: 'bg-transparent hover:bg-gray-100 text-gray-700'
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl'
  };

  const baseClasses = `
    ${variants[variant]} 
    ${sizes[size]} 
    ${className}
    relative overflow-hidden
    transition-all duration-300
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
  `;

  const buttonVariants = {
    initial: { scale: 1 },
    hover: { 
      scale: 1.05,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    },
    tap: { 
      scale: 0.95,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    }
  };

  const rippleVariants = {
    initial: { scale: 0, opacity: 0.5 },
    animate: { 
      scale: 4, 
      opacity: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const [ripples, setRipples] = React.useState([]);

  const createRipple = (event) => {
    if (disabled) return;
    
    const button = event.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    const newRipple = {
      x,
      y,
      size,
      id: Date.now()
    };

    setRipples(prev => [...prev, newRipple]);
    
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);

    if (onClick) onClick(event);
  };

  const LoadingSpinner = () => (
    <motion.div
      className="w-5 h-5 border-2 border-current border-t-transparent rounded-full"
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  );

  return (
    <motion.button
      className={baseClasses}
      variants={buttonVariants}
      initial="initial"
      whileHover={!disabled ? "hover" : "initial"}
      whileTap={!disabled ? "tap" : "initial"}
      onClick={createRipple}
      disabled={disabled || loading}
      {...props}
    >
      {/* Ripple effects */}
      {ripples.map(ripple => (
        <motion.span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full pointer-events-none"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
          }}
          variants={rippleVariants}
          initial="initial"
          animate="animate"
        />
      ))}

      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        initial={{ x: '-100%' }}
        whileHover={{ x: '100%' }}
        transition={{ duration: 0.6 }}
      />

      {/* Content */}
      <span className="relative z-10 flex items-center justify-center space-x-2">
        {loading ? (
          <LoadingSpinner />
        ) : (
          <>
            {icon && (
              <motion.span
                initial={{ rotate: 0 }}
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                {icon}
              </motion.span>
            )}
            <span>{children}</span>
          </>
        )}
      </span>
    </motion.button>
  );
};

// Floating Action Button
export const FloatingActionButton = ({ 
  children, 
  className = '', 
  onClick,
  position = 'bottom-right',
  ...props 
}) => {
  const positions = {
    'bottom-right': 'fixed bottom-6 right-6',
    'bottom-left': 'fixed bottom-6 left-6',
    'top-right': 'fixed top-6 right-6',
    'top-left': 'fixed top-6 left-6'
  };

  return (
    <motion.button
      className={`
        ${positions[position]}
        w-14 h-14 bg-gradient-to-r from-green-400 to-blue-500
        rounded-full shadow-lg hover:shadow-xl
        flex items-center justify-center
        text-white text-xl
        z-50
        ${className}
      `}
      whileHover={{ 
        scale: 1.1,
        rotate: 360,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.9 }}
      onClick={onClick}
      {...props}
    >
      {children}
    </motion.button>
  );
};

// Button with icon animation
export const IconButton = ({ 
  icon, 
  children, 
  iconPosition = 'left',
  className = '',
  ...props 
}) => {
  return (
    <AnimatedButton
      className={`flex items-center space-x-2 ${className}`}
      {...props}
    >
      {iconPosition === 'left' && icon && (
        <motion.span
          whileHover={{ x: 2 }}
          transition={{ type: "spring", stiffness: 400 }}
        >
          {icon}
        </motion.span>
      )}
      <span>{children}</span>
      {iconPosition === 'right' && icon && (
        <motion.span
          whileHover={{ x: 2 }}
          transition={{ type: "spring", stiffness: 400 }}
        >
          {icon}
        </motion.span>
      )}
    </AnimatedButton>
  );
};

export default AnimatedButton;
