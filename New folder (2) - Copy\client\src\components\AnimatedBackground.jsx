import React from 'react';
import { motion } from 'framer-motion';

const AnimatedBackground = ({ children, variant = 'default' }) => {
  const variants = {
    default: {
      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
    },
    nature: {
      background: 'linear-gradient(135deg, #22c55e 0%, #059669 25%, #047857 50%, #065f46 75%, #064e3b 100%)',
    },
    glass: {
      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
      backdropFilter: 'blur(20px)',
    }
  };

  const FloatingElement = ({ delay = 0, size = 'md', color = 'green' }) => {
    const sizes = {
      sm: 'w-4 h-4',
      md: 'w-8 h-8',
      lg: 'w-12 h-12',
      xl: 'w-16 h-16'
    };

    const colors = {
      green: 'bg-green-400/20',
      blue: 'bg-blue-400/20',
      yellow: 'bg-yellow-400/20',
      purple: 'bg-purple-400/20'
    };

    return (
      <motion.div
        className={`absolute rounded-full ${sizes[size]} ${colors[color]} blur-sm`}
        animate={{
          y: [0, -30, 0],
          x: [0, 15, 0],
          rotate: [0, 180, 360],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 8 + delay,
          repeat: Infinity,
          ease: "easeInOut",
          delay: delay
        }}
        style={{
          top: `${Math.random() * 100}%`,
          left: `${Math.random() * 100}%`,
        }}
      />
    );
  };

  const ParticleField = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(20)].map((_, i) => (
        <FloatingElement
          key={i}
          delay={i * 0.5}
          size={['sm', 'md', 'lg'][Math.floor(Math.random() * 3)]}
          color={['green', 'blue', 'yellow', 'purple'][Math.floor(Math.random() * 4)]}
        />
      ))}
    </div>
  );

  const GeometricShapes = () => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Animated circles */}
      <motion.div
        className="absolute w-96 h-96 rounded-full border border-green-200/30"
        style={{ top: '10%', left: '80%' }}
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      
      <motion.div
        className="absolute w-64 h-64 rounded-full bg-gradient-to-r from-green-400/10 to-blue-400/10"
        style={{ top: '60%', left: '10%' }}
        animate={{
          scale: [1, 1.3, 1],
          x: [0, 50, 0],
          y: [0, -30, 0],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Animated triangles */}
      <motion.div
        className="absolute w-32 h-32"
        style={{ 
          top: '20%', 
          left: '20%',
          clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
          background: 'linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(59, 130, 246, 0.1))'
        }}
        animate={{
          rotate: [0, 360],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );

  return (
    <div 
      className="relative min-h-screen overflow-hidden"
      style={variants[variant]}
    >
      {variant === 'default' && <ParticleField />}
      {variant === 'nature' && <GeometricShapes />}
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-green-500/5 pointer-events-none" />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default AnimatedBackground;
