import React from 'react';
import { motion } from 'framer-motion';
import { FaLeaf, FaTrophy, FaFire, FaChartLine, FaArrowUp, FaCalendarAlt, FaUsers, FaBullseye } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { StaggerContainer, StaggerItem, ScrollReveal, HoverScale } from '../components/PageTransition';

const Dashboard = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  const stats = [
    {
      icon: <FaTrophy className="text-yellow-500" />,
      label: 'Total Points',
      value: user?.ecoProfile?.totalPoints || 0,
      color: 'yellow'
    },
    {
      icon: <FaFire className="text-red-500" />,
      label: 'Current Streak',
      value: user?.ecoProfile?.streak || 0,
      color: 'red'
    },
    {
      icon: <FaChartLine className="text-blue-500" />,
      label: 'Level',
      value: user?.ecoProfile?.level || 1,
      color: 'blue'
    },
    {
      icon: <FaLeaf className="text-green-500" />,
      label: 'CO₂ Saved',
      value: `${user?.ecoProfile?.impact?.co2Saved || 0} kg`,
      color: 'green'
    }
  ];

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="mb-12"
        >
          <div className="card-glass p-8 text-center relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-400/20 to-green-400/20 rounded-full translate-y-12 -translate-x-12"></div>

            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="relative z-10"
            >
              <h1 className="text-4xl md:text-5xl font-black text-gray-900 mb-4">
                Welcome back, <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">{user?.name}</span>! 🌱
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                Ready to make a positive impact today?
              </p>

              {/* Quick stats */}
              <div className="flex justify-center items-center space-x-8 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <FaCalendarAlt className="text-green-500" />
                  <span>Day {user?.ecoProfile?.streak || 1}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaBullseye className="text-blue-500" />
                  <span>Level {user?.ecoProfile?.level || 1}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FaUsers className="text-purple-500" />
                  <span>Top 10%</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {stats.map((stat, index) => (
            <StaggerItem key={index}>
              <HoverScale scale={1.05}>
                <motion.div
                  className="card-eco text-center group relative overflow-hidden"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Animated background */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    initial={false}
                  />

                  {/* Icon with pulse animation */}
                  <motion.div
                    className="text-5xl mb-4 relative z-10"
                    animate={{
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: index * 0.2
                    }}
                  >
                    {stat.icon}
                  </motion.div>

                  {/* Animated counter */}
                  <motion.div
                    className="text-3xl md:text-4xl font-black text-gray-900 mb-2 relative z-10"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{
                      type: "spring",
                      stiffness: 100,
                      delay: index * 0.1 + 0.5
                    }}
                  >
                    {stat.value}
                    {index === 0 && (
                      <motion.div
                        className="inline-block ml-2"
                        animate={{ y: [-2, 2, -2] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <FaArrowUp className="text-green-500 text-lg" />
                      </motion.div>
                    )}
                  </motion.div>

                  <div className="text-gray-600 font-semibold relative z-10 group-hover:text-green-600 transition-colors">
                    {stat.label}
                  </div>

                  {/* Progress indicator */}
                  <motion.div
                    className="mt-4 h-2 bg-gray-200 rounded-full overflow-hidden relative z-10"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 0.5, delay: index * 0.1 + 1 }}
                  >
                    <motion.div
                      className={`h-full bg-gradient-to-r ${
                        stat.color === 'yellow' ? 'from-yellow-400 to-orange-400' :
                        stat.color === 'red' ? 'from-red-400 to-pink-400' :
                        stat.color === 'blue' ? 'from-blue-400 to-indigo-400' :
                        'from-green-400 to-emerald-400'
                      } rounded-full`}
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min((typeof stat.value === 'string' ? parseInt(stat.value) : stat.value) * 2, 100)}%` }}
                      transition={{ duration: 1, delay: index * 0.1 + 1.2 }}
                    />
                  </motion.div>
                </motion.div>
              </HoverScale>
            </StaggerItem>
          ))}
        </StaggerContainer>

        {/* Quick Actions */}
        <ScrollReveal>
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              Quick Actions 🚀
            </h2>
          </div>
        </ScrollReveal>

        <StaggerContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <StaggerItem>
            <HoverScale scale={1.03}>
              <motion.div
                className="card-eco group relative overflow-hidden"
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl flex items-center justify-center">
                      <FaLeaf className="text-white text-xl" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-600 transition-colors">
                      Today's Habits
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Track your daily eco-friendly habits and build sustainable routines
                  </p>
                  <motion.button
                    className="btn-eco w-full group-hover:shadow-lg"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    View Habits
                  </motion.button>
                </div>
              </motion.div>
            </HoverScale>
          </StaggerItem>

          <StaggerItem>
            <HoverScale scale={1.03}>
              <motion.div
                className="card group relative overflow-hidden"
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center">
                      <FaChartLine className="text-white text-xl" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      Environmental Impact
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    See your positive environmental impact and CO₂ savings
                  </p>
                  <motion.button
                    className="btn-secondary w-full group-hover:shadow-lg"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    View Impact
                  </motion.button>
                </div>
              </motion.div>
            </HoverScale>
          </StaggerItem>

          <StaggerItem>
            <HoverScale scale={1.03}>
              <motion.div
                className="card group relative overflow-hidden"
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Gradient background */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center">
                      <FaUsers className="text-white text-xl" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors">
                      Community Challenges
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Join challenges and compete with other eco-warriors
                  </p>
                  <motion.button
                    className="btn-secondary w-full group-hover:shadow-lg"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    View Challenges
                  </motion.button>
                </div>
              </motion.div>
            </HoverScale>
          </StaggerItem>
        </StaggerContainer>

        {/* Recent Activity */}
        <ScrollReveal>
          <div className="card-glass relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">
                Recent Activity 📈
              </h3>
              <motion.button
                className="text-green-600 hover:text-green-700 font-semibold text-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                View All
              </motion.button>
            </div>

            <StaggerContainer className="space-y-4">
              <StaggerItem>
                <motion.div
                  className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 hover:border-green-200 transition-all duration-300"
                  whileHover={{ x: 5, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <FaLeaf className="text-white text-lg" />
                  </motion.div>
                  <div className="flex-1">
                    <p className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                      Completed "Use public transport"
                    </p>
                    <p className="text-sm text-gray-500">2 hours ago • +15 points</p>
                  </div>
                  <motion.div
                    className="text-green-500 opacity-0 group-hover:opacity-100 transition-opacity"
                    initial={{ x: -10 }}
                    whileHover={{ x: 0 }}
                  >
                    <FaArrowUp className="transform rotate-45" />
                  </motion.div>
                </motion.div>
              </StaggerItem>

              <StaggerItem>
                <motion.div
                  className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 hover:border-blue-200 transition-all duration-300"
                  whileHover={{ x: 5, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <FaTrophy className="text-white text-lg" />
                  </motion.div>
                  <div className="flex-1">
                    <p className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      Earned 10 eco points
                    </p>
                    <p className="text-sm text-gray-500">5 hours ago • Level progress</p>
                  </div>
                  <motion.div
                    className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity"
                    initial={{ x: -10 }}
                    whileHover={{ x: 0 }}
                  >
                    <FaArrowUp className="transform rotate-45" />
                  </motion.div>
                </motion.div>
              </StaggerItem>

              <StaggerItem>
                <motion.div
                  className="group flex items-center space-x-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-100 hover:border-yellow-200 transition-all duration-300"
                  whileHover={{ x: 5, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <motion.div
                    className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <FaFire className="text-white text-lg" />
                  </motion.div>
                  <div className="flex-1">
                    <p className="font-semibold text-gray-900 group-hover:text-yellow-600 transition-colors">
                      Reached 7-day streak! 🔥
                    </p>
                    <p className="text-sm text-gray-500">Yesterday • Achievement unlocked</p>
                  </div>
                  <motion.div
                    className="text-yellow-500 opacity-0 group-hover:opacity-100 transition-opacity"
                    initial={{ x: -10 }}
                    whileHover={{ x: 0 }}
                  >
                    <FaArrowUp className="transform rotate-45" />
                  </motion.div>
                </motion.div>
              </StaggerItem>
            </StaggerContainer>
          </div>
        </ScrollReveal>
      </div>
    </div>
  );
};

export default Dashboard;
