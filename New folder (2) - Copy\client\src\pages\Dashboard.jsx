import React from 'react';
import { motion } from 'framer-motion';
import { FaLeaf, FaTrophy, FaFire, FaChartLine } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const Dashboard = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  const stats = [
    {
      icon: <FaTrophy className="text-yellow-500" />,
      label: 'Total Points',
      value: user?.ecoProfile?.totalPoints || 0,
      color: 'yellow'
    },
    {
      icon: <FaFire className="text-red-500" />,
      label: 'Current Streak',
      value: user?.ecoProfile?.streak || 0,
      color: 'red'
    },
    {
      icon: <FaChartLine className="text-blue-500" />,
      label: 'Level',
      value: user?.ecoProfile?.level || 1,
      color: 'blue'
    },
    {
      icon: <FaLeaf className="text-green-500" />,
      label: 'CO₂ Saved',
      value: `${user?.ecoProfile?.impact?.co2Saved || 0} kg`,
      color: 'green'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.name}! 🌱
          </h1>
          <p className="text-gray-600">
            Ready to make a positive impact today?
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="card text-center"
            >
              <div className="text-3xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {stat.value}
              </div>
              <div className="text-gray-600 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
        >
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Today's Habits
            </h3>
            <p className="text-gray-600 mb-4">
              Track your daily eco-friendly habits
            </p>
            <button className="btn-eco w-full">
              View Habits
            </button>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Environmental Impact
            </h3>
            <p className="text-gray-600 mb-4">
              See your positive environmental impact
            </p>
            <button className="btn-secondary w-full">
              View Impact
            </button>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Community Challenges
            </h3>
            <p className="text-gray-600 mb-4">
              Join challenges with other eco-warriors
            </p>
            <button className="btn-secondary w-full">
              View Challenges
            </button>
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recent Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <FaLeaf className="text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Completed "Use public transport"
                </p>
                <p className="text-xs text-gray-500">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <FaTrophy className="text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Earned 10 eco points
                </p>
                <p className="text-xs text-gray-500">5 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
              <FaFire className="text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Reached 7-day streak!
                </p>
                <p className="text-xs text-gray-500">Yesterday</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
