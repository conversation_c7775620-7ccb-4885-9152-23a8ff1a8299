import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaLeaf,
  FaBars,
  FaTimes,
  FaUser,
  FaSignOutAlt,
  FaCog,
  FaGlobe,
  FaChevronDown,
  FaBell,
  FaSearch
} from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const { isAuthenticated, user, logout } = useAuth();
  const { t, currentLanguage, changeLanguage, availableLanguages } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = async () => {
    await logout();
    navigate('/');
    setShowUserMenu(false);
  };

  const navLinks = [
    { path: '/', label: t('home'), public: true },
    { path: '/dashboard', label: t('dashboard'), protected: true },
    { path: '/habits', label: t('habits'), protected: true },
    { path: '/impact', label: t('impact'), protected: true },
    { path: '/community', label: t('community'), protected: true },
    { path: '/challenges', label: t('challenges'), protected: true },
    { path: '/map', label: t('map'), protected: true },
    { path: '/ai-assistant', label: t('aiAssistant'), protected: true },
  ];

  const filteredNavLinks = navLinks.filter(link => 
    link.public || (link.protected && isAuthenticated)
  );

  return (
    <motion.nav
      className={`
        fixed top-0 left-0 right-0 z-50 transition-all duration-500
        ${scrolled
          ? 'bg-white/95 backdrop-blur-lg shadow-xl border-b border-white/20'
          : 'bg-white/90 backdrop-blur-sm shadow-lg'
        }
      `}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-20">
          {/* Logo */}
          <motion.div
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400 }}
          >
            <Link to="/" className="flex items-center space-x-3 group">
              <motion.div
                className="relative"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                  <FaLeaf className="text-white text-xl" />
                </div>
                {/* Glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl opacity-0 group-hover:opacity-30 blur-lg transition-opacity duration-300"></div>
              </motion.div>
              <div className="flex flex-col">
                <span className="text-2xl font-black bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  GreenMate
                </span>
                <span className="text-xs text-gray-500 font-medium -mt-1">Eco Lifestyle</span>
              </div>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {filteredNavLinks.map((link, index) => (
              <motion.div
                key={link.path}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
              >
                <Link
                  to={link.path}
                  className={`
                    relative px-4 py-2 rounded-xl font-semibold text-sm
                    transition-all duration-300 group overflow-hidden
                    ${location.pathname === link.path
                      ? 'text-white bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg'
                      : 'text-gray-700 hover:text-green-600'
                    }
                  `}
                >
                  {/* Background hover effect */}
                  {location.pathname !== link.path && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      layoutId="navbar-hover"
                    />
                  )}

                  {/* Active indicator */}
                  {location.pathname === link.path && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl"
                      layoutId="navbar-active"
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    />
                  )}

                  <span className="relative z-10">{link.label}</span>

                  {/* Shine effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100"
                    initial={{ x: '-100%' }}
                    whileHover={{ x: '100%' }}
                    transition={{ duration: 0.6 }}
                  />
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Right side - Auth & Language */}
          <div className="hidden md:flex items-center space-x-3">
            {/* Search Button */}
            <motion.button
              className="p-3 rounded-xl text-gray-600 hover:text-green-600 hover:bg-green-50 transition-all duration-300 group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FaSearch className="text-lg group-hover:scale-110 transition-transform" />
            </motion.button>

            {/* Notifications */}
            {isAuthenticated && (
              <motion.button
                className="relative p-3 rounded-xl text-gray-600 hover:text-green-600 hover:bg-green-50 transition-all duration-300 group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaBell className="text-lg group-hover:scale-110 transition-transform" />
                {/* Notification badge */}
                <motion.div
                  className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-400 to-pink-500 rounded-full flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 1, type: "spring", stiffness: 500 }}
                >
                  <span className="text-xs text-white font-bold">3</span>
                </motion.div>
              </motion.button>
            )}

            {/* Language Selector */}
            <div className="relative">
              <motion.button
                onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                className="flex items-center space-x-2 px-4 py-2 rounded-xl text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-300 group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <FaGlobe className="text-lg group-hover:scale-110 transition-transform" />
                <span className="font-medium">{availableLanguages.find(lang => lang.code === currentLanguage)?.flag}</span>
                <FaChevronDown className={`text-sm transition-transform duration-300 ${showLanguageMenu ? 'rotate-180' : ''}`} />
              </motion.button>
              
              <AnimatePresence>
                {showLanguageMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    className="absolute right-0 mt-3 w-56 bg-white/95 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 py-2 z-50"
                  >
                    {availableLanguages.map((language, index) => (
                      <motion.button
                        key={language.code}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        onClick={() => {
                          changeLanguage(language.code);
                          setShowLanguageMenu(false);
                        }}
                        className={`
                          w-full text-left px-4 py-3 text-sm font-medium
                          hover:bg-green-50 flex items-center space-x-3
                          transition-all duration-200 group
                          ${currentLanguage === language.code
                            ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-600 border-l-4 border-green-500'
                            : 'text-gray-700 hover:text-green-600'
                          }
                        `}
                      >
                        <span className="text-lg">{language.flag}</span>
                        <span className="group-hover:translate-x-1 transition-transform">{language.name}</span>
                        {currentLanguage === language.code && (
                          <motion.div
                            className="ml-auto w-2 h-2 bg-green-500 rounded-full"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: "spring", stiffness: 500 }}
                          />
                        )}
                      </motion.button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {isAuthenticated ? (
              /* User Menu */
              <div className="relative">
                <motion.button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-3 px-4 py-2 rounded-xl text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-300 group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.div
                    className="relative"
                    whileHover={{ scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 400 }}
                  >
                    <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                      {user?.name?.charAt(0).toUpperCase()}
                    </div>
                    {/* Online indicator */}
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
                  </motion.div>
                  <div className="hidden lg:block">
                    <div className="text-sm font-semibold">{user?.name}</div>
                    <div className="text-xs text-gray-500">Level {user?.ecoProfile?.level || 1}</div>
                  </div>
                  <FaChevronDown className={`text-sm transition-transform duration-300 ${showUserMenu ? 'rotate-180' : ''}`} />
                </motion.button>

                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                      className="absolute right-0 mt-3 w-64 bg-white/95 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 py-3 z-50"
                    >
                      {/* User info header */}
                      <div className="px-4 py-3 border-b border-gray-100">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold">
                            {user?.name?.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900">{user?.name}</div>
                            <div className="text-sm text-gray-500">{user?.email}</div>
                            <div className="text-xs text-green-600 font-medium">Level {user?.ecoProfile?.level || 1} • {user?.ecoProfile?.totalPoints || 0} points</div>
                          </div>
                        </div>
                      </div>

                      {/* Menu items */}
                      <div className="py-2">
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 }}
                        >
                          <Link
                            to="/profile"
                            className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-200 group"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <FaUser className="text-lg group-hover:scale-110 transition-transform" />
                            <span className="group-hover:translate-x-1 transition-transform">{t('profile')}</span>
                          </Link>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.2 }}
                        >
                          <Link
                            to="/settings"
                            className="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-200 group"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <FaCog className="text-lg group-hover:scale-110 transition-transform" />
                            <span className="group-hover:translate-x-1 transition-transform">{t('settings')}</span>
                          </Link>
                        </motion.div>

                        <div className="border-t border-gray-100 mt-2 pt-2">
                          <motion.button
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 }}
                            onClick={handleLogout}
                            className="w-full flex items-center space-x-3 px-4 py-3 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200 group"
                          >
                            <FaSignOutAlt className="text-lg group-hover:scale-110 transition-transform" />
                            <span className="group-hover:translate-x-1 transition-transform">{t('logout')}</span>
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              /* Auth Links */
              <div className="flex items-center space-x-3">
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Link
                    to="/login"
                    className="px-4 py-2 text-gray-700 hover:text-green-600 font-semibold text-sm transition-all duration-300 hover:bg-green-50 rounded-xl"
                  >
                    {t('login')}
                  </Link>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Link
                    to="/register"
                    className="btn-eco text-sm px-6 py-2 shadow-lg hover:shadow-xl"
                  >
                    {t('register')}
                  </Link>
                </motion.div>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center">
            <motion.button
              onClick={() => setIsOpen(!isOpen)}
              className="p-3 rounded-xl text-gray-700 hover:text-green-600 hover:bg-green-50 focus:outline-none transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={{ rotate: isOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
              </motion.div>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="lg:hidden bg-white/95 backdrop-blur-lg border-t border-white/20"
          >
            <div className="px-4 pt-4 pb-6 space-y-2">
              {/* Mobile navigation links */}
              {filteredNavLinks.map((link, index) => (
                <motion.div
                  key={link.path}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    to={link.path}
                    className={`
                      block px-4 py-3 rounded-xl font-semibold text-base
                      transition-all duration-300 group
                      ${location.pathname === link.path
                        ? 'text-white bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg'
                        : 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                      }
                    `}
                    onClick={() => setIsOpen(false)}
                  >
                    <span className="group-hover:translate-x-1 transition-transform inline-block">
                      {link.label}
                    </span>
                  </Link>
                </motion.div>
              ))}

              {/* Mobile auth section */}
              {!isAuthenticated ? (
                <div className="pt-4 border-t border-gray-200 space-y-2">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Link
                      to="/login"
                      className="block px-4 py-3 rounded-xl font-semibold text-base text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-300"
                      onClick={() => setIsOpen(false)}
                    >
                      {t('login')}
                    </Link>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7 }}
                  >
                    <Link
                      to="/register"
                      className="block px-4 py-3 rounded-xl font-semibold text-base bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                      onClick={() => setIsOpen(false)}
                    >
                      {t('register')}
                    </Link>
                  </motion.div>
                </div>
              ) : (
                <div className="pt-4 border-t border-gray-200">
                  {/* Mobile user info */}
                  <div className="flex items-center space-x-3 px-4 py-3 mb-3 bg-green-50 rounded-xl">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold">
                      {user?.name?.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{user?.name}</div>
                      <div className="text-sm text-green-600">Level {user?.ecoProfile?.level || 1}</div>
                    </div>
                  </div>

                  <motion.button
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-3 rounded-xl font-semibold text-base text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-300"
                  >
                    <div className="flex items-center space-x-2">
                      <FaSignOutAlt />
                      <span>{t('logout')}</span>
                    </div>
                  </motion.button>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;
